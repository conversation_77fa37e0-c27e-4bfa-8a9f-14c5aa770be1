using Alicres.SerialPort.Models;

namespace Alicres.SerialPort.Interfaces;

/// <summary>
/// 串口通讯服务接口
/// </summary>
public interface ISerialPortService : IDisposable
{
    /// <summary>
    /// 当前串口配置
    /// </summary>
    SerialPortConfiguration Configuration { get; }

    /// <summary>
    /// 当前串口状态
    /// </summary>
    SerialPortStatus Status { get; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    event EventHandler<SerialPortStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 错误发生事件
    /// </summary>
    event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 获取系统中可用的串口列表
    /// </summary>
    /// <returns>可用串口名称数组</returns>
    string[] GetAvailablePorts();

    /// <summary>
    /// 配置串口参数
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <exception cref="Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
    void Configure(SerialPortConfiguration configuration);

    /// <summary>
    /// 打开串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功打开返回 true，否则返回 false</returns>
    /// <exception cref="Exceptions.SerialPortConnectionException">连接失败时抛出</exception>
    Task<bool> OpenAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 关闭串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功关闭返回 true，否则返回 false</returns>
    Task<bool> CloseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际发送的字节数</returns>
    /// <exception cref="Exceptions.SerialPortDataException">发送失败时抛出</exception>
    Task<int> SendAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送文本数据
    /// </summary>
    /// <param name="text">要发送的文本</param>
    /// <param name="encoding">编码方式，默认为 UTF-8</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际发送的字节数</returns>
    /// <exception cref="Exceptions.SerialPortDataException">发送失败时抛出</exception>
    Task<int> SendTextAsync(string text, System.Text.Encoding? encoding = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 读取数据
    /// </summary>
    /// <param name="buffer">接收缓冲区</param>
    /// <param name="offset">缓冲区偏移量</param>
    /// <param name="count">要读取的字节数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际读取的字节数</returns>
    /// <exception cref="Exceptions.SerialPortDataException">读取失败时抛出</exception>
    Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// 读取所有可用数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的数据</returns>
    /// <exception cref="Exceptions.SerialPortDataException">读取失败时抛出</exception>
    Task<byte[]> ReadAllAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    void ClearReceiveBuffer();

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    void ClearSendBuffer();

    /// <summary>
    /// 获取接收缓冲区中的字节数
    /// </summary>
    /// <returns>缓冲区中的字节数</returns>
    int GetBytesToRead();

    /// <summary>
    /// 获取发送缓冲区中的字节数
    /// </summary>
    /// <returns>缓冲区中的字节数</returns>
    int GetBytesToWrite();
}


