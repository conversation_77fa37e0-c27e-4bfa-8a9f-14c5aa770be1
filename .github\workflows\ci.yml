name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        dotnet-version: ['8.0.x']

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ matrix.dotnet-version }}

    - name: Cache NuGet packages
      uses: actions/cache@v3
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Run tests
      run: dotnet test --no-build --configuration Release --collect:"XPlat Code Coverage" --results-directory TestResults --logger "console;verbosity=normal"

    - name: Generate coverage report
      run: |
        dotnet tool install -g dotnet-reportgenerator-globaltool
        reportgenerator -reports:"TestResults/**/coverage.cobertura.xml" -targetdir:"TestResults/CoverageReport" -reporttypes:Html

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        files: TestResults/**/coverage.cobertura.xml
        fail_ci_if_error: true

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.dotnet-version }}
        path: TestResults/

    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report-${{ matrix.dotnet-version }}
        path: TestResults/CoverageReport/

    - name: Pack NuGet packages
      run: dotnet pack --no-build --configuration Release --output ./packages

    - name: Upload NuGet packages
      uses: actions/upload-artifact@v4
      with:
        name: nuget-packages-${{ matrix.dotnet-version }}
        path: ./packages/*.nupkg

  quality-gate:
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Run code analysis
      run: dotnet build --configuration Release --verbosity normal

    - name: Check test coverage threshold
      run: |
        dotnet tool install -g dotnet-reportgenerator-globaltool
        # 下载测试结果
        # 这里需要从前一个job下载测试结果，实际实现时需要调整
        echo "Quality gate: Checking if test coverage >= 80%"
        # 实际的覆盖率检查逻辑将在后续实现

  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Run security audit
      run: dotnet list package --vulnerable --include-transitive

    - name: Run dependency check
      run: dotnet list package --outdated
